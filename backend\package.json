{"name": "gigmosaic-backend", "version": "1.0.0", "description": "Backend API for GigMosaic review system with photo uploads", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "aws-sdk": "^2.1498.0", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["express", "typescript", "mongodb", "s3", "reviews", "api"], "author": "GigMosaic Team", "license": "MIT"}
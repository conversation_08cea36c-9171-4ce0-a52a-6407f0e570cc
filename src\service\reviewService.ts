/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';

// Review interfaces
export interface Review {
  id: string;
  title: string;
  name: string;
  date: string;
  rating: number;
  review: string;
  images: string[];
  imageUrls: string[];
  profileImage: string;
  serviceId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateReviewData {
  providerId?: string;
  serviceId?: string;
  serviceName?: string;
  bookingId?: string;
  rating: number;
  title: string;
  review: string;
  comment?: string;
  images?: File[];
  imageUrls?: string[];
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
}

export interface UpdateReviewData {
  rating?: number;
  title?: string;
  review?: string;
  images?: File[];
  imageUrls?: string[];
}

export interface ReviewsResponse {
  reviews: Review[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  userId?: string;
}

/**
 * Create a new review
 */
export const createReview = async (data: CreateReviewData): Promise<Review> => {
  try {
    console.log('Creating review with data:', data);

    // Prepare review payload with S3 URLs
    const reviewPayload = {
      providerId: data.providerId || 'default-provider', // Provide default if not available
      serviceId: data.serviceId || '',
      bookingId: data.bookingId || '',
      rating: data.rating,
      title: data.title,
      review: data.review,
      comment: data.comment || data.review, // Use comment field as required by backend
      date: new Date().toISOString(),
      serviceRating: data.serviceRating,
      qualityRating: data.qualityRating,
      valueRating: data.valueRating,
      communicationRating: data.communicationRating,
      imageUrls: data.imageUrls || [] // Send S3 URLs directly
    };

    const response = await apiClient.post(Path.reviews, reviewPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Error creating review:', error);
    logger.error('Error creating review:', error);
    throw error;
  }
};

/**
 * Get all reviews with pagination
 */
export const getAllReviews = async (params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 3, userId } = params;
    console.log(`Fetching reviews - page: ${page}, limit: ${limit}, userId: ${userId}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      queryParams.append('userId', userId);
    }

    const response = await apiClient.get(`${Path.reviews}?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching reviews:', error);
    logger.error('Error fetching reviews:', error);
    throw error;
  }
};

/**
 * Get a specific review by ID
 */
export const getReviewById = async (reviewId: string): Promise<Review> => {
  try {
    console.log(`Fetching review with ID: ${reviewId}`);
    const response = await apiClient.get(`${Path.reviews}/${reviewId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching review ${reviewId}:`, error);
    logger.error(`Error fetching review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Update a review
 */
export const updateReview = async (reviewId: string, data: UpdateReviewData): Promise<Review> => {
  try {
    console.log(`Updating review ${reviewId} with data:`, data);
    const response = await apiClient.put(`${Path.reviews}/${reviewId}`, data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating review ${reviewId}:`, error);
    logger.error(`Error updating review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Delete a review
 */
export const deleteReview = async (reviewId: string): Promise<void> => {
  try {
    console.log(`Deleting review with ID: ${reviewId}`);
    await apiClient.delete(`${Path.reviews}/${reviewId}`);
  } catch (error: any) {
    console.error(`Error deleting review ${reviewId}:`, error);
    logger.error(`Error deleting review ${reviewId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by user ID
 */
export const getReviewsByUserId = async (userId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for user ${userId} - page: ${page}, limit: ${limit}`);
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      userId: userId,
    }).toString();
    
    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for user ${userId}:`, error);
    logger.error(`Error fetching reviews for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get reviews by service ID
 */
export const getReviewsByServiceId = async (serviceId: string, params: PaginationParams = {}): Promise<ReviewsResponse> => {
  try {
    const { page = 1, limit = 10 } = params;
    console.log(`Fetching reviews for service ${serviceId} - page: ${page}, limit: ${limit}`);
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      serviceId: serviceId,
    }).toString();
    
    const response = await apiClient.get(`${Path.reviews}?${queryParams}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching reviews for service ${serviceId}:`, error);
    logger.error(`Error fetching reviews for service ${serviceId}:`, error);
    throw error;
  }
};
